{"name": "hx", "version": "3.8.9", "description": "华星称重管理系统", "author": "华星", "license": "MIT", "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:8080 && electron .\"", "electron:build": "npm run build:prod && electron-builder", "electron:build-win": "npm run build:prod && electron-builder --win", "electron:build-mac": "npm run build:prod && electron-builder --mac", "electron:build-linux": "npm run build:prod && electron-builder --linux"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "@tailwindcss/postcss7-compat": "^2.2.17", "autoprefixer": "^9.8.8", "axios": "0.28.1", "clipboard": "2.0.8", "core-js": "3.37.1", "echarts": "5.4.0", "element-resize-detector": "^1.2.4", "element-ui": "2.15.14", "file-saver": "2.0.5", "flv.js": "^1.6.2", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "hiprint": "^0.3.0", "i18n-jsautotranslate": "^3.17.0", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "jsmpeg-player": "^3.0.3", "nprogress": "0.2.0", "postcss": "^7.0.39", "quill": "2.0.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "three": "^0.121.0", "vanta": "^0.5.24", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-json-viewer": "^2.2.22", "vue-meta": "2.4.0", "vue-plugin-hiprint": "^0.0.60", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "concurrently": "^9.2.0", "connect": "3.6.6", "electron": "^37.2.6", "electron-builder": "^26.0.12", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "postcss-px-to-viewport": "^1.1.1", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vite": "^4.5.14", "vite-auto-i18n-plugin": "^1.1.1", "vite-plugin-html": "^3.2.2", "vite-plugin-vue2": "^2.0.3", "vue-template-compiler": "2.6.12", "wait-on": "^8.0.4", "webpack-auto-i18n-plugin": "^1.1.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "build": {"appId": "com.huaxing.weight-management", "productName": "华星称重管理系统", "directories": {"output": "dist_electron"}, "nativeRebuilder": "sequential", "buildDependenciesFromSource": false, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/favicon.ico", "requestedExecutionLevel": "asInvoker", "signAndEditExecutable": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "华星称重管理系统"}, "mac": {"target": "dmg", "icon": "public/favicon.ico", "category": "public.app-category.business"}, "linux": {"target": "AppImage", "icon": "public/favicon.ico", "category": "Office"}}}